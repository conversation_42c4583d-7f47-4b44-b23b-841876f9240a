﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-SRJ.Web-78266e55-039e-47ce-8271-79bed130efe3</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="bundleconfig.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="jQuery" Version="3.7.1" />
    <PackageReference Include="jQuery.Validation" Version="1.21.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.18" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.18" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.18" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.18">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="3.10.0" />
    <PackageReference Include="Microsoft.Identity.Web.MicrosoftGraph" Version="3.10.0" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.10.0" />
    <PackageReference Include="Microsoft.jQuery.Unobtrusive.Ajax" Version="3.2.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NToastNotify" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SRJ.WebCore\SRJ.WebCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Views\Shared\Application\" />
    <Folder Include="wwwroot\Files\" />
    <Folder Include="wwwroot\lib\jquery-ajax-unobtrusive\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="bundleconfig.json" />
  </ItemGroup>
</Project>
