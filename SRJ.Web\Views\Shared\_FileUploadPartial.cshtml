﻿@model string

<div class="collapse" id="collapseUpload_@Model">
    <div class="form-group w-75 my-2">
        <div class="input-group">
            <input type="file" id="fileUpload_@Model" class="form-control form-control-sm" data-doc-type="@Model" />
            <button type="button" class="btn btn-primary btn-sm" onclick="uploadFile('@Model')">
                <i class="bi bi-cloud-plus"></i>
            </button>
        </div>
    </div>
</div>

<script>
    function uploadFile(documentType) {
        var fileInput = document.querySelectorAll('input[data-doc-type="' + documentType + '"]');
        var formData = new FormData();

        formData.append("documentType", documentType); // Ensure correct case
        formData.append("applicationId", '@ViewData["ApplicationId"]');

        let fileFound = false;

        fileInput.forEach(function (input) {
            if (input.files.length > 0) {
                formData.append("file", input.files[0]);
                fileFound = true;
            }
        });

        if (!fileFound) {
            showToast('No file selected for upload. Please choose a file.', 'error');
            return;
        }

        const apiBaseUrl = '@ViewData["ApiBaseUrl"]';

        fetch(`${apiBaseUrl}/api/v1/Document/UploadDocument`, {
            method: 'POST',
            body: formData,
            headers: {
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
            }
        })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { throw err });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showToast(documentType + ' uploaded successfully.', 'success');
                    document.querySelectorAll('input[name="' + documentType + '"]').forEach(function (checkbox) {
                        checkbox.checked = true;
                    });
                    syncCheckboxes(documentType);  // Sync the checkbox state
                } else {
                    console.error('Failed to upload file:', documentType, data.message);
                    showToast('Failed to upload ' + documentType + '. Please try again.', 'error');
                }
            })
            .catch(error => {
                console.error('Error uploading file:', error);
                showToast('Error uploading file. Please try again.', 'error');
            });
    }

    function showToast(message, type) {
        if (type === 'success') {
            toastr.success(message);
        } else if (type === 'error') {
            toastr.error(message);
        }
    }

    function syncCheckboxes(checkboxName) {
        var isChecked = document.querySelectorAll('input[name="' + checkboxName + '"]:checked').length > 0;
        document.querySelectorAll('input[name="' + checkboxName + '"]').forEach(function (checkbox) {
            checkbox.checked = isChecked;
        });
    }
</script>