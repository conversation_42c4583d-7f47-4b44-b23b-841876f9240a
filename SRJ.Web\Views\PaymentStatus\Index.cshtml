﻿@using SRJ.DataAccess.Enums
@using SRJ.DataAccess.Entities
@using SRJ.CommonCore.ViewModels.Application
@model IEnumerable<PaymentStatusTableVM>

@{
    ViewData["Title"] = "Payment Status";
    ViewData["Create"] = true;

    int index = 1;
}

<div class="page-heading">
    <div class="page-title">
        <div class="row">
            <div class="col-12 col-md-6 order-md-1 order-last">
                <h3>Payment Status List</h3>
            </div>
            <div class="col-12 col-md-6 order-md-2 order-first">
                <nav aria-label="breadcrumb" class="breadcrumb-header float-start float-lg-end">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="Home">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Payment Status List</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>
<div class="page-content">
    <section class="section">
        <div id="PaymentStatusPage">
            <div class="PaymentStatusPage-form-outer">
                <div class="card">
                    <div class="card-header">
                        <a asp-action="Create" class="btn btn-primary float-sm-right">
                            <i class="bi bi-plus-circle me-1"></i>
                            Create new
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select id="invoiceStatusFilter" class="form-control">
                                    <option value="">Filter by Invoice Status</option>
                                    <option value="Pending">Pending</option>
                                    <option value="Paid">Paid</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select id="paymentMethodFilter" class="form-control">
                                    <option value="">Filter by Payment Method</option>
                                    <option value="Cash">Cash</option>
                                    <option value="Knet">Knet</option>
                                    <option value="Bank">Bank</option>
                                </select>
                            </div>
                        </div>
                        <table id="datatable" class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Details</th>
                                    <th>Name</th>
                                    <th>Civil ID</th>
                                    <th>Mobile Number</th>
                                    <th>Invoice Status</th>
                                    <th>Payment Method</th>
                                    <th>Created Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@(index++)</td>
                                        <td>
                                            <div class="btn-group" role="group" aria-label="Basic example">
                                                <a asp-action="Details" asp-route-id="@item.Id" title="View Details">
                                                    <i class="bi bi-list"></i>
                                                </a>

                                                <a asp-action="Edit" asp-route-id="@item.Id" title="Edit" class="mx-2">
                                                    <i class="bi bi-pencil-square"></i>
                                                </a>

                                                <a asp-action="Delete" asp-route-id="@item.Id" class="delete-button"
                                                   onclick="return confirm('Are you sure you want to delete this Payment Status?');"
                                                   title="Delete">
                                                    <i class="bi bi-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                        <td>@item.CustomerName</td>
                                        <td>@item.CivilId</td>
                                        <td>@item.CustomerMobile</td>
                                        <td>@item.InvoiceStatus</td>
                                        <td>@item.PaymentMethod</td>
                                        <td>@item.CreatedDate</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Initialize the DataTable
            var table = $("#datatable").DataTable({
                "responsive": true,
                "lengthChange": true,
                "autoWidth": false,
                "buttons": ["csv", "excel", "pdf", "colvis"]
            });

            // Custom filtering function for Invoice Status and Payment Method
            $.fn.dataTable.ext.search.push(
                function (settings, data, dataIndex) {
                    var invoiceStatusFilter = $('#invoiceStatusFilter').val();
                    var paymentMethodFilter = $('#paymentMethodFilter').val();

                    var invoiceStatus = data[5]; // Invoice Status column index
                    var paymentMethod = data[6]; // Payment Method column index

                    if ((invoiceStatusFilter && invoiceStatusFilter !== invoiceStatus) ||
                        (paymentMethodFilter && paymentMethodFilter !== paymentMethod)) {
                        return false;
                    }
                    return true;
                }
            );

            // Event listeners for filter inputs
            $('#invoiceStatusFilter, #paymentMethodFilter').on('change', function () {
                table.draw();
            });

            console.log("DataTable initialized:", table);
        });
    </script>

    <script>
        $(document).ready(function () {
            // Fetch the current user's roles
            $.ajax({
                url: '/api/v1/UserRole/roles',
                method: 'GET',
                success: function (roles) {
                    applyRoles(roles);
                },
                error: function () {
                    console.error('Failed to fetch user roles');
                }
            });

            function applyRoles(roles) {
                const adminRole = 'Admin';

                // If the user does not have the admin role, hide the delete buttons
                if (!roles.includes(adminRole)) {
                    $('.delete-button').hide();
                }
            }
        });
    </script>
}