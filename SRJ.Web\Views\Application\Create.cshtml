@using SRJ.CommonCore.ViewModels.Application
@using SRJ.DataAccess.Enums.Application
@model ApplicationFormVM

@{
    ViewData["Title"] = "Create Applicant Profile Page";
}

@section Styles {
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>

    <style>
        .progress {
            border-radius: 1rem;
            background-color: #e9ecef;
        }

        .card-title {
            font-weight: 700 !important;
            color: black;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            display: none;
        }

        .sender-name {
            font-size: 0.8rem;
            color: #888;
            margin-top: 0.5rem;
            text-align: right;
        }
    </style>

    <link rel="stylesheet" href="~/assets/extensions/choices.js/public/assets/styles/choices.css" />
    <link rel="stylesheet" href="~/assets/extensions/flatpickr/flatpickr.min.css" />
}

<div class="page-heading">
    <div class="page-title">
        <div class="row">
            <div class="col-12 col-md-12 order-md-2 order-first">
                <div class="card mb-1">
                    <div class="card-body py-4 px-4">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3>Applicant Profile</h3>
                            </div>
                            <div class="d-flex align-items-center">
                                @*
                                <div class="name me-3">
                                <h5 dir="rtl" class="font-bold">@Model.Name</h5>
                                <h6 class="text-muted mb-0">@Model.ApplicationStage</h6>
                                </div>
                                <div class="avatar avatar-xl">
                                @{
                                if (Model.Gender?.ToLower() == "male")
                                {
                                <img src="./assets/compiled/jpg/2.jpg" alt="Face 1">
                                }
                                else
                                {
                                <img src="./assets/compiled/jpg/3.jpg" alt="Face 1">

                                }
                                }
                                </div>
                                *@
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="page-content">
    <section id="multiple-column-form">
        <div class="row match-height">
            <div class="col-12">
                <div id="basicwizard">
                    <ul class="nav nav-pills nav-justified mb-3">
                        <li class="nav-item" data-target-form="#BasicInfoForm">
                            <a href="#BasicInfo"
                               data-bs-toggle="tab"
                               data-toggle="tab"
                               class="nav-link icon-btn active">
                                <i class="bx bxs-contact me-1"></i>
                                <span class="d-none d-sm-inline">
                                    Basic Information
                                </span>
                            </a>
                        </li>
                        @*
                        <!-- end nav item -->
                        <li class="nav-item" data-target-form="#ReceptionInfoForm">
                        <a href="#ReceptionInfo"
                        data-bs-toggle="tab"
                        data-toggle="tab"
                        class="nav-link icon-btn">
                        <i class="bx bxs-contact me-1"></i>
                        <span class="d-none d-sm-inline">
                        Reception
                        </span>
                        </a>
                        </li>
                        *@
                        <!-- end nav item -->
                        <li class="nav-item" data-target-form="#AdmissionInfoForm">
                            <a href="#AdmissionInfo"
                               data-bs-toggle="tab"
                               data-toggle="tab"
                               class="nav-link icon-btn">
                                <i class="bx bxs-building me-1"></i>
                                <span class="d-none d-sm-inline">Admission</span>
                            </a>
                        </li>
                        <!-- end nav item -->
                        <li class="nav-item" data-target-form="#PaymentsInfoForm">
                            <a href="#PaymentsInfo"
                               data-bs-toggle="tab"
                               data-toggle="tab"
                               class="nav-link icon-btn">
                                <i class="bx bxs-wallet me-1"></i>
                                <span class="d-none d-sm-inline">Payments</span>
                            </a>
                        </li>
                        <!-- end nav item -->
                        <li class="nav-item" data-target-form="#FilesInfoForm">
                            <a href="#FilesInfo"
                               data-bs-toggle="tab"
                               data-toggle="tab"
                               class="nav-link icon-btn">
                                <i class="bx bxs-file-plus me-1"></i>
                                <span class="d-none d-sm-inline">Files</span>
                            </a>
                        </li>
                        <!-- end nav item -->
                        <li class="nav-item" data-target-form="#PlacementTestInfoForm">
                            <a href="#PlacementTestInfo"
                               data-bs-toggle="tab"
                               data-toggle="tab"
                               class="nav-link icon-btn">
                                <i class="bx bxs-file me-1"></i>
                                <span class="d-none d-sm-inline">Placement Test</span>
                            </a>
                        </li>
                        <!-- end nav item -->
                        <li class="nav-item">
                            <a href="#ApplicationInfo"
                               data-bs-toggle="tab"
                               data-toggle="tab"
                               class="nav-link icon-btn">
                                <i class="bx bxs-check-circle me-1"></i>
                                <span class="d-none d-sm-inline">Application Submission</span>
                            </a>
                        </li>
                        <!-- end nav item -->
                    </ul>
                    <!-- nav pills -->

                    <form asp-action="Create" asp-controller="Application" method="post" id="main-form" data-ajax="true" data-ajax-method="post" data-ajax-begin="onBegin" data-ajax-success="onSuccess" data-ajax-failure="onError" enctype="multipart/form-data">
                        <div class="tab-content mb-0 pt-0">

                            <!-- START: Define your progress bar here -->
                            @*style="height: 15px;"*@
                            <div id="bar" class="progress mb-3">
                                <div class="bar progress-bar progress-bar-striped progress-bar-animated bg-blue"></div>
                            </div>
                            <!-- END: Define your progress bar here -->
                            @Html.AntiForgeryToken()
                            <!-- START: Define your tab pans here -->
                            <partial name="/Views/Shared/Application/_BasicInfo.cshtml" />

                            @* <partial name="/Views/Shared/Application/_ReceptionInfo.cshtml" /> *@

                            <partial name="/Views/Shared/Application/_AdmissionInfo.cshtml" />

                            <partial name="/Views/Shared/Application/_PaymentsInfo.cshtml" />

                            <partial name="/Views/Shared/Application/_FilesInfo.cshtml" />

                            <partial name="/Views/Shared/Application/_PlacementTestInfo.cshtml" />

                            <partial name="/Views/Shared/Application/_ApplicationInfo.cshtml" />

                            <!-- END: Define your tab pans here -->
                            <!-- START: Define your controller buttons here-->
                            <div class="d-flex wizard justify-content-between">
                                <div class="first">
                                    <a href="javascript:void(0);" class="btn btn-primary">
                                        First
                                    </a>
                                </div>
                                <div class="d-flex">
                                    <div class="previous me-2">
                                        <a href="javascript:void(0);" class="btn icon-btn btn-primary">
                                            <i class="bx bx-left-arrow-alt me-2"></i>
                                            Previous
                                        </a>
                                    </div>
                                    <div class="next">
                                        <a href="javascript:void(0);"
                                           class="btn icon-btn btn-primary">
                                            Next
                                            <i class="bx bx-right-arrow-alt ms-2"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="last">
                                    <a href="javascript:void(0);" class="btn btn-primary">
                                        Last
                                    </a>
                                </div>
                            </div>
                            <!-- END: Define your controller buttons here-->
                        </div>
                    </form>
                    <!-- end tab content-->
                </div>
                <!-- end basicwizard -->
            </div>
        </div>
    </section>
    <!-- // Basic multiple Column Form section end -->
</div>

@section Scripts {
    <!--Import vanilla wizard here-->
    <script src="https://cdn.jsdelivr.net/npm/vanilla-wizard@0.0.5">
    </script>

    <!--Initializing a wizard with no configuration -->
    <script>
        new Wizard("#basicwizard", {
            progress: true
        });
    </script>

    <script src="~/assets/extensions/choices.js/public/assets/scripts/choices.js"></script>
    <script src="~/assets/static/js/pages/form-element-select.js"></script>

    <script src="~/assets/extensions/flatpickr/flatpickr.min.js"></script>
    <script src="~/assets/static/js/pages/date-picker.js"></script>

    <script type="text/javascript">
        $(document).ready(function () {
            var isSubmitting = false; // Flag to check if the form is already being submitted

            // Function to handle tab changes
            function handleTabChange() {
                var hash = window.location.hash;
                $('a[href="' + hash + '"]').tab('show');
            }

            // Check if there's a hash in the URL on load
            handleTabChange();

            // On clicking a tab, update the URL hash
            $('.nav-link').on('click', function (e) {
                var tabId = $(this).attr('href');
                window.location.hash = tabId; // Append tab id to URL as hash
                handleTabChange(); // Handle the tab change if needed
            });

            // On window hash change, switch to the appropriate tab
            $(window).on('hashchange', function () {
                handleTabChange();
            });

            // Define the onBegin function
            function onBegin() {
                var submitButton = $('#main-form .action-button');
                // Hide the submit button and show the spinner only if it doesn't already exist
                submitButton.hide();
                if ($('#eform-loader').length === 0) {
                    submitButton.after('<button id="eform-loader" class="btn icon-btn m-3 btn-secondary" type="button" disabled><span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Processing...</button>');
                }
            }

            // Define the onSuccess function
            function onSuccess(response) {
                $('#eform-loader').remove(); // Remove loading spinner
                isSubmitting = false; // Reset the flag
                if (response.success) {
                    // Disable all form fields after submission
                    $('form input, form textarea, form button, form select').prop('disabled', true);

                    // Redirect to the Edit page of the newly created application
                    window.location.href = '/Application/Edit/' + response.id;
                } else {
                    toastr.error('Failed to create Application. Please check your input.');
                    $('.action-button').show(); // Show the submit button again
                }
            }

            // Define the onError function
            function onError() {
                $('#eform-loader').remove(); // Remove loading spinner
                toastr.error('An error occurred while submitting the form.');
                $('.action-button').show(); // Show the submit button again
                isSubmitting = false; // Reset the flag
            }

            // AJAX form submission
            $('#main-form').submit(function (e) {
                e.preventDefault();

                if (isSubmitting) {
                    return; // Prevent multiple submissions
                }

                isSubmitting = true; // Set the flag to indicate form is being submitted

                var form = $(this);
                var url = form.attr('action');

                // Enable all form fields before submission
                $('form input, form textarea, form button, form select').prop('disabled', false);

                $.ajax({
                    type: 'POST',
                    url: url,
                    data: form.serialize(),
                    beforeSend: onBegin,
                    success: onSuccess,
                    error: onError
                });
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            // Fetch the current user's roles
            $.ajax({
                url: '/api/UserRole/roles',
                method: 'GET',
                success: function (roles) {
                    applyRoles(roles);
                    console.log(roles);
                },
                error: function () {
                    console.error('Failed to fetch user roles');
                }
            });

            function applyRoles(roles) {
                const teamToFormMap = {
                    'Reception': ['#BasicInfo', '#ReceptionInfo'],
                    'Advising': ['#AdmissionInfo'],
                    'PlacementTest': ['#PlacementTestInfo'],
                    'ApplicationSubmission': ['#ApplicationInfo'],
                    'Payment': ['#PaymentsInfo', '#AdmissionInfo', '#FilesInfo', '#PlacementTestInfo', '#BasicInfo', '#ReceptionInfo', '#ApplicationInfo'],
                    'Documents': ['#FilesInfo', '#PlacementTestInfo']
                };

                const adminRole = 'Admin';
                const salesAgentRole = 'SalesAgent';
                const topManagementRole = 'TopManagement';

                // If the user has the admin or sales agent role, enable all forms and exit early
                if (roles.includes(adminRole) || roles.includes(salesAgentRole) || roles.includes(topManagementRole)) {
                    // Enable all forms for admin or sales agent
                    $('form input, form textarea, form button, form select').prop('disabled', false);
                    return;
                }

                // Disable all forms
                $('form input, form textarea, form button, form select').prop('disabled', true);

                // Enable CommentBox-form for all
                $('#CommentBox-form input, #CommentBox-form textarea, #CommentBox-form button').prop('disabled', false);

                // Enable forms based on the user's roles
                roles.forEach(role => {
                    if (teamToFormMap[role]) {
                        teamToFormMap[role].forEach(formId => {
                            $(formId + ' input, ' + formId + ' textarea, ' + formId + ' button, ' + formId + ' select').prop('disabled', false);
                        });
                    }
                });

                $('#Reception-Docs-form input, #Reception-Docs-form textarea, #Reception-Docs-form button, #Reception-Docs-form select').prop('disabled', true);
            }
        });
    </script>

    <script>
        $(document).ready(function () {
            $('#civilId').change(function () {
                var civilId = $(this).val();
                $.ajax({
                    url: '/api/UserRole/check-civil-id',
                    type: 'GET',
                    data: { civilId: civilId },
                    success: function (data) {
                        if (data.exists) {
                            // Civil ID already exists, show error message and disable submit button
                            $('#civilIdError').html('Civil ID already exists. Click <a href="Edit/' + data.uniqueId + '">here</a> to view the profile.');
                            $('.action-button').prop('disabled', true);
                            console.log("Civil ID exists");
                        } else {
                            // Civil ID does not exist, hide error message and enable submit button
                            $('#civilIdError').text('');
                            $('.action-button').prop('disabled', false);
                            console.log("Civil ID does not exist");
                        }
                    }
                });
            });
        });
    </script>
}