﻿@using SRJ.CommonCore.ViewModels.Application
@using SRJ.DataAccess.Enums.Application
@model ApplicationFormVM

@{
    ViewData["Title"] = "Edit Applicant Profile Page";
}

@{
    string badgeText;
    string badgeClass;

    switch (Model.ApplicationStatus)
    {
        case ApplicationStatus.Complete:
            badgeClass = "badge badge-pill bg-light-success me-1";
            break;
        case ApplicationStatus.Incomplete:
            badgeClass = "badge badge-pill bg-light-danger me-1";
            break;
        case ApplicationStatus.Pending:
            badgeClass = "badge badge-pill bg-light-warning me-1";
            break;
        default:
            badgeText = "Unknown";
            badgeClass = "badge badge-pill bg-light-secondary me-1";
            break;
    }
}

@section Styles {
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>

    <style>
        .progress {
            border-radius: 1rem;
            background-color: #e9ecef;
        }

        .card-title {
            font-weight: 700 !important;
            color: black;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            display: none;
        }

        .sender-name {
            font-size: 0.8rem;
            color: #888;
            margin-top: 0.5rem;
            text-align: right;
        }
    </style>

    <link rel="stylesheet" href="~/assets/extensions/choices.js/public/assets/styles/choices.css" />
    <link rel="stylesheet" href="~/assets/extensions/flatpickr/flatpickr.min.css" />
}

<div class="page-heading">
    <div class="page-title">
        <div class="row">
            <div class="col-12 col-md-12 order-md-2 order-first">
                <div class="card mb-1">
                    <div class="card-body py-4 px-4">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3>Applicant Profile</h3>
                                <h6 class="text-muted mb-0">@Model.ApplicationStage</h6>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="name me-3">
                                    <h5 dir="rtl" class="font-bold">@Model.Name</h5>
                                    <span class="@badgeClass">
                                        @Model.ApplicationStatus
                                    </span>
                                </div>
                                <div class="avatar avatar-xl">
                                    @{
                                        if (Model.Gender == UserGender.Male)
                                        {
                                            <img src="~/assets/compiled/jpg/2.jpg" alt="Face 1" />
                                        }
                                        else
                                        {
                                            <img src="~/assets/compiled/jpg/3.jpg" alt="Face 2" />
                                        }
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="page-content">
    <section id="multiple-column-form">
        <div class="row match-height">
            <div class="col-12">
                <div id="basicwizard">
                    <ul class="nav nav-pills nav-justified mb-3">
                        <li class="nav-item" data-target-form="#BasicInfoForm">
                            <a href="#BasicInfo" data-bs-toggle="tab" data-toggle="tab"
                               class="nav-link icon-btn active">
                                <i class="bx bxs-contact me-1"></i>
                                <span class="d-none d-sm-inline">
                                    Basic Information
                                </span>
                            </a>
                        </li>
                        @*
                        <!-- end nav item -->
                        <li class="nav-item" data-target-form="#ReceptionInfoForm">
                        <a href="#ReceptionInfo"
                        data-bs-toggle="tab"
                        data-toggle="tab"
                        class="nav-link icon-btn">
                        <i class="bx bxs-contact me-1"></i>
                        <span class="d-none d-sm-inline">
                        Reception
                        </span>
                        </a>
                        </li>
                        *@
                        <!-- end nav item -->
                        <li class="nav-item" data-target-form="#AdmissionInfoForm">
                            <a href="#AdmissionInfo" data-bs-toggle="tab" data-toggle="tab" class="nav-link icon-btn">
                                <i class="bx bxs-building me-1"></i>
                                <span class="d-none d-sm-inline">Admission</span>
                            </a>
                        </li>
                        <!-- end nav item -->
                        <li class="nav-item" data-target-form="#PaymentsInfoForm">
                            <a href="#PaymentsInfo" data-bs-toggle="tab" data-toggle="tab" class="nav-link icon-btn">
                                <i class="bx bxs-wallet me-1"></i>
                                <span class="d-none d-sm-inline">Payments</span>
                            </a>
                        </li>
                        <!-- end nav item -->
                        <li class="nav-item" data-target-form="#FilesInfoForm">
                            <a href="#FilesInfo" data-bs-toggle="tab" data-toggle="tab" class="nav-link icon-btn">
                                <i class="bx bxs-file-plus me-1"></i>
                                <span class="d-none d-sm-inline">Files</span>
                            </a>
                        </li>
                        <!-- end nav item -->
                        <li class="nav-item" data-target-form="#PlacementTestInfoForm">
                            <a href="#PlacementTestInfo" data-bs-toggle="tab" data-toggle="tab"
                               class="nav-link icon-btn">
                                <i class="bx bxs-file me-1"></i>
                                <span class="d-none d-sm-inline">Placement Test</span>
                            </a>
                        </li>
                        <!-- end nav item -->
                        <li class="nav-item">
                            <a href="#ApplicationInfo" data-bs-toggle="tab" data-toggle="tab" class="nav-link icon-btn">
                                <i class="bx bxs-check-circle me-1"></i>
                                <span class="d-none d-sm-inline">Application Submission</span>
                            </a>
                        </li>
                        <!-- end nav item -->
                    </ul>
                    <!-- nav pills -->
                    <form asp-action="Edit" asp-controller="Application" method="post" id="main-form" data-ajax="true" data-ajax-method="post" data-ajax-begin="onBegin" data-ajax-success="onSuccess" data-ajax-failure="onError" enctype="multipart/form-data">
                        <div class="tab-content mb-0 pt-0">

                            <!-- START: Define your progress bar here -->
                            @*style="height: 15px;"*@
                            <div id="bar" class="progress mb-3">
                                <div class="bar progress-bar progress-bar-striped progress-bar-animated bg-blue"></div>
                            </div>
                            <!-- END: Define your progress bar here -->
                            @Html.AntiForgeryToken()
                            <!-- START: Define your tab pans here -->
                            <partial name="/Views/Shared/Application/_BasicInfo.cshtml" />

                            @* <partial name="/Views/Shared/Application/_ReceptionInfo.cshtml" /> *@

                            <partial name="/Views/Shared/Application/_AdmissionInfo.cshtml" />

                            <partial name="/Views/Shared/Application/_PaymentsInfo.cshtml" />

                            <partial name="/Views/Shared/Application/_FilesInfo.cshtml" />

                            <partial name="/Views/Shared/Application/_PlacementTestInfo.cshtml" />

                            <partial name="/Views/Shared/Application/_ApplicationInfo.cshtml" />

                            <!-- END: Define your tab pans here -->
                            <!-- START: Define your controller buttons here-->
                            <div class="d-flex wizard justify-content-between">
                                <div class="first">
                                    <a href="javascript:void(0);" class="btn btn-primary">
                                        First
                                    </a>
                                </div>
                                <div class="d-flex">
                                    <div class="previous me-2">
                                        <a href="javascript:void(0);" class="btn icon-btn btn-primary">
                                            <i class="bx bx-left-arrow-alt me-2"></i>
                                            Previous
                                        </a>
                                    </div>
                                    <div class="next">
                                        <a href="javascript:void(0);" class="btn icon-btn btn-primary">
                                            Next
                                            <i class="bx bx-right-arrow-alt ms-2"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="last">
                                    <a href="javascript:void(0);" class="btn btn-primary">
                                        Last
                                    </a>
                                </div>
                            </div>
                            <!-- END: Define your controller buttons here-->
                        </div>
                    </form>
                    <!-- end tab content-->
                </div>
                <!-- end basicwizard -->
            </div>
        </div>
    </section>
    <!-- // Basic multiple Column Form section end -->
</div>

@section Scripts {
    <!--Import vanilla wizard here-->
    <script src="https://cdn.jsdelivr.net/npm/vanilla-wizard@0.0.5">
    </script>

    <!--Initializing a wizard with no configuration -->
    <script>
        new Wizard("#basicwizard", {
            progress: true
        });
    </script>

    <script src="~/assets/extensions/choices.js/public/assets/scripts/choices.js"></script>
    <script src="~/assets/static/js/pages/form-element-select.js"></script>

    <script src="~/assets/extensions/flatpickr/flatpickr.min.js"></script>
    <script src="~/assets/static/js/pages/date-picker.js"></script>

    <script type="text/javascript">
        $(document).ready(function () {
            var isSubmitting = false; // Flag to check if the form is already being submitted

            // Function to handle tab changes
            function handleTabChange() {
                var hash = window.location.hash;
                $('a[href="' + hash + '"]').tab('show');
            }

            // Check if there's a hash in the URL on load
            handleTabChange();

            // On clicking a tab, update the URL hash
            $('.nav-link').on('click', function (e) {
                var tabId = $(this).attr('href');
                window.location.hash = tabId; // Append tab id to URL as hash
                handleTabChange(); // Handle the tab change if needed
            });

            // On window hash change, switch to the appropriate tab
            $(window).on('hashchange', function () {
                handleTabChange();
            });

            // Define the onBegin function
            function onBegin() {
                var submitButton = $('#main-form .action-button');
                // Hide the submit button and show the spinner only if it doesn't already exist
                submitButton.hide();
                if ($('#eform-loader').length === 0) {
                    submitButton.after('<button id="eform-loader" class="btn icon-btn m-3 btn-secondary" type="button" disabled><span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Processing...</button>');
                }
            }

            // Define the onSuccess function
            function onSuccess(response) {
                $('#eform-loader').remove(); // Remove loading spinner
                isSubmitting = false; // Reset the flag
                if (response.success) {
                    // Disable all form fields after submission
                    //$('form input, form textarea, form button, form select').prop('disabled', true);

                    setTimeout(function () {
                        location.reload();
                    }, 1500);
                } else {
                    toastr.error('Failed to edit Application. Please check your input.');
                    $('.action-button').show(); // Show the submit button again
                }
            }

            // Define the onError function
            function onError() {
                $('#eform-loader').remove(); // Remove loading spinner
                toastr.error('An error occurred while submitting the form.');
                $('.action-button').show(); // Show the submit button again
                isSubmitting = false; // Reset the flag
            }

            // AJAX form submission
            $('#main-form').submit(function (e) {
                e.preventDefault();

                if (isSubmitting) {
                    return; // Prevent multiple submissions
                }

                isSubmitting = true; // Set the flag to indicate form is being submitted

                var form = $(this);
                var url = form.attr('action');

                // Enable all form fields before submission
                $('form input, form textarea, form button, form select').prop('disabled', false);

                $.ajax({
                    type: 'POST',
                    url: url,
                    data: form.serialize(),
                    beforeSend: onBegin,
                    success: onSuccess,
                    error: onError
                });
            });

            // Add the action-button click event listener
            $('.action-button').on('click', function () {
                var stage = $(this).closest('.tab-pane').data('stage');
                $('input[name="ApplicationStage"]').val(stage);
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            const apiBaseUrl = '@ViewData["ApiBaseUrl"]';

            $('#sync-grade-btn').on('click', function () {
                var form = $('#main-form');

                // Temporarily enable all form fields before serialization
                var allFields = $('form input, form textarea, form button, form select');
                var disabledFields = allFields.filter(':disabled');
                disabledFields.prop('disabled', false);

                var formData = form.serializeArray();
                var jsonData = {};

                $.each(formData, function (index, field) {
                    // Handling boolean fields
                    if (field.value === "true" || field.value === "false") {
                        jsonData[field.name] = (field.value === "true");
                    }
                    // Handling specific fields that should be strings
                    else if (field.name === "MobileNumber" || field.name === "CivilId") {
                        jsonData[field.name] = String(field.value);
                    }
                    // Handling numeric fields
                    else if (!isNaN(field.value) && field.value !== "") {
                        jsonData[field.name] = Number(field.value);
                    }
                    // Handling empty fields
                    else if (field.value === "") {
                        jsonData[field.name] = null;
                    }
                    // Handling all other fields as strings
                    else {
                        jsonData[field.name] = String(field.value);
                    }
                });

                // Restore the disabled state of the fields that were disabled
                disabledFields.prop('disabled', true);

                if (!jsonData.CivilId) {
                    toastr.error('Civil ID is required.');
                    return;
                }

                function onBegin() {
                    var submitButton = $('#sync-grade-btn');
                    submitButton.hide();
                    if ($('#eform-loader').length === 0) {
                        submitButton.after('<button id="eform-loader" class="btn icon-btn m-3 btn-secondary" type="button" disabled><span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Processing...</button>');
                    }
                }

                $.ajax({
                    url: `${apiBaseUrl}/api/v1/Grade/PTSubmitForm`,
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(jsonData),
                    beforeSend: onBegin,
                    success: function (response) {
                        $('#eform-loader').remove(); // Remove the loader
                        $('#sync-grade-btn').show(); // Show the submit button again

                        if (response.success) {
                            var data = response.data;
                            $('#PlacementTestUser').val(data.placementTestUser);
                            $('#ReadingTest').val(data.readingTest);
                            $('#GrammarTest').val(data.grammarTest);
                            $('#WritingTest').val(data.writingTest);
                            $('#MathTest').val(data.mathTest);
                            $('#ComputerTest').val(data.computerTest);
                            $('#TotalScore').val(data.totalScore);
                            $('#EntryLevel').val(data.entryLevel);

                            toastr.success('Grades synchronized successfully.');
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function (xhr) {
                        $('#eform-loader').remove(); // Remove the loader
                        $('#sync-grade-btn').show(); // Show the submit button again
                        var errorMessage = xhr.responseJSON?.title || 'An error occurred while syncing grades.';
                        toastr.error(errorMessage);
                    }
                });
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            $('#lostReasonDropdown').on('change', function () {
                if ($(this).val() === 'Other') {
                    $('#lostReasonOther').show().prop('required', true);
                } else {
                    $('#lostReasonOther').hide().prop('required', false);
                }
            });

            // Initial check if the LostReasonOther should be shown
            if ($('#lostReasonDropdown').val() === 'Other') {
                $('#lostReasonOther').show().prop('required', true);
            }

            $('#rejectReasonDropdown').on('change', function () {
                if ($(this).val() === 'Other') {
                    $('#rejectReasonOther').show().prop('required', true);
                } else {
                    $('#rejectReasonOther').hide().prop('required', false);
                }
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            // Function to sync checkbox states
            function syncCheckboxes(checkboxName) {
                var isChecked = $('input[name="' + checkboxName + '"]:checked').length > 0;
                $('input[name="' + checkboxName + '"]').prop('checked', isChecked);
            }

            // Attach event listeners to checkboxes
            $('input[type=checkbox]').on('change', function () {
                var checkboxName = $(this).attr('name');
                var isChecked = $(this).prop('checked');
                $('input[name="' + checkboxName + '"]').prop('checked', isChecked);
            });

            // Initialize the checkbox states on page load
            $('input[type=checkbox]').each(function () {
                var checkboxName = $(this).attr('name');
                syncCheckboxes(checkboxName);
            });
        });
    </script>

    <script>
        function getSelectedItems() {
            var items = [];
            if ($('#PucFees10kdReceipt').is(":checked") && !$('#PucFees10kdReceipt').next().next().hasClass('fee-paid')) items.push('PUC Fees');
            if ($('#TestFees15kd').is(":checked") && !$('#TestFees15kd').next().next().hasClass('fee-paid')) items.push('Test Fees');
            if ($('#ApplicationFees15kd').is(":checked") && !$('#ApplicationFees15kd').next().next().hasClass('fee-paid')) items.push('Application Fees');
            if ($('#SeatReservationFees').is(":checked") && !$('#SeatReservationFees').next().next().hasClass('fee-paid')) items.push('Seat Reservation Fees');
            return items.join(', ');
        }

        $(document).ready(function () {
            var isSubmitting = false; // Flag to check if the form is already being submitted

            // Disable the pay-link button by default
            $('#pay-link').prop('disabled', true);

            // Declare the fee amounts
            var PucFees = 10;
            var TestFees = 15;
            var ApplicationFees = 15;
            var SeatReservationFees = 150;

            function calculateTotal() {
                var total = 0;
                if ($('#PucFees10kdReceipt').is(":checked") && !$('#PucFees10kdReceipt').next().next().hasClass('fee-paid')) total += PucFees;
                if ($('#TestFees15kd').is(":checked") && !$('#TestFees15kd').next().next().hasClass('fee-paid')) total += TestFees;
                if ($('#ApplicationFees15kd').is(":checked") && !$('#ApplicationFees15kd').next().next().hasClass('fee-paid')) total += ApplicationFees;
                if ($('#SeatReservationFees').is(":checked") && !$('#SeatReservationFees').next().next().hasClass('fee-paid')) total += SeatReservationFees;
                return total;
            }

            function updateTotalFees() {
                var cash = $('#pay-cash').val() != "" ? $('#pay-cash').val() : '0';
                var knet = $('#pay-knet').val() != "" ? $('#pay-knet').val() : '0';
                var bank = $('#pay-bank').val() != "" ? $('#pay-bank').val() : '0';
                var total = $('#pay-total').val() != "" ? $('#pay-total').val() : calculateTotal();

                $('#totalFees').val(cash + ',' + knet + ',' + bank + ',' + total);
            }

            function validatePaymentFields() {
                var cash = Number($('#pay-cash').val()) || 0;
                var knet = Number($('#pay-knet').val()) || 0;
                var bank = Number($('#pay-bank').val()) || 0;
                var total = Number($('#pay-total').val()) || calculateTotal();

                if ((cash + knet + bank) === 0) {
                    $('#payment-error').removeClass('d-none').text('Please fill in at least one payment field.');
                    $('.action-button').prop('disabled', true);
                    $('#pay-link').prop('disabled', true);
                    return false;
                }

                if ((cash + knet + bank) !== total) {
                    $('#payment-error').removeClass('d-none').text('The total payment must match the calculated total');
                    $('#pay-link').prop('disabled', true);
                    $('.action-button').prop('disabled', true);
                    return false;
                }

                $('#payment-error').addClass('d-none').text('');
                $('#pay-link').prop('disabled', bank === 0); // Disable pay-link if bank is 0
                $('.action-button').prop('disabled', false);
                return true;
            }

            function toggleKnetReference() {
                var knetValue = $('#pay-knet').val();
                if (knetValue && knetValue > 0) {
                    $('#knet-reference-group').removeClass('d-none');
                    $('#knet-reference').prop('required', true);
                } else {
                    $('#knet-reference-group').addClass('d-none');
                    $('#knet-reference').prop('required', false);
                }
            }

            $(".fee-checkbox").change(function () {
                var total = calculateTotal();
                $('#pay-total').val(total);
                updateTotalFees();
                validatePaymentFields();
            });

            $('#pay-cash, #pay-knet, #pay-bank').on('input', function () {
                updateTotalFees();
                validatePaymentFields();
            });

            $('#pay-total').on('input', function () {
                var total = Number($('#pay-total').val()) || 0;
                var calculatedTotal = calculateTotal();

                if (total !== calculatedTotal) {
                    $('#payment-error').removeClass('d-none').text('The manually entered total does not match the calculated total.');
                    $('.action-button').prop('disabled', true);
                    $('#pay-link').prop('disabled', true);
                } else {
                    $('#payment-error').addClass('d-none').text('');
                    validatePaymentFields();
                }
            });

            $('#pay-knet').on('input', toggleKnetReference);

            // Ensure sendWhatsAppPaymentLink requires bank payment
            function sendWhatsAppPaymentLink(event) {
                event.preventDefault();

                var bank = $('#pay-bank').val() || 0;
                if (bank == 0) {
                    $('#payment-error').removeClass('d-none').text('Bank payment is required for sending the payment link.');
                    return;
                }

                const apiBaseUrl = '@ViewData["ApiBaseUrl"]'; // Fetch the base URL once

                var userId = '@Model.Id';
                var url = `${apiBaseUrl}/api/v1/Payment/SendWhatsAppPaymentLink`; // Use the apiBaseUrl variable

                // Get selected items and create a JSON string
                var selectedItems = getSelectedItems(); // e.g., ["PUC Fees", "Test Fees"]
                var cash = $('#pay-cash').val() || 0;
                var knet = $('#pay-knet').val() || 0;
                var knetReference = $('#knet-reference').val() || '';

                var itemDetails = {
                    items: selectedItems,
                    fees: {
                        cash: cash,
                        knet: knet,
                        knetReference: knetReference,
                        online: bank
                    }
                };
                var itemName = JSON.stringify(itemDetails);

                var data = {
                    userId: userId,
                    itemName: itemName,
                    totalCost: bank,
                    CreatedBy: '@ViewData["CreatedBy"]',
                    PaymentMethod: "Bank" // Set the payment method
                };

                function onBegin() {
                    var submitButton = $('#main-form .send-whatsapp');
                    // Hide the submit button and show the spinner only if it doesn't already exist
                    submitButton.hide();
                    if ($('#eform-loader').length === 0) {
                        submitButton.after('<button id="eform-loader" class="btn icon-btn m-3 btn-secondary" type="button" disabled><span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Processing...</button>');
                    }
                }

                $.ajax({
                    url: url,
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    beforeSend: onBegin,
                    success: function (response) {
                        $('#eform-loader').remove(); // Remove the loader
                        $('#main-form .send-whatsapp').show(); // Show the submit button again

                        if (response.success) {
                            var whatsappUrl = response.whatsappUrl;
                            window.open(whatsappUrl, '_blank');
                            toastr.success('Payment link sent successfully.');
                        } else {
                            toastr.error('Failed to send payment link: ' + response.message);
                        }
                    },
                    error: function () {
                        $('#eform-loader').remove(); // Remove the loader
                        $('#main-form .send-whatsapp').show(); // Show the submit button again
                        toastr.error('An error occurred while sending the payment link.');
                    }
                });
            }

            $('#pay-link').on('click', sendWhatsAppPaymentLink);

            function manualPayment(event) {
                event.preventDefault();

                var cash = $('#pay-cash').val() || 0;
                var knet = $('#pay-knet').val() || 0;
                var knetReference = $('#knet-reference').val() || '';

                if (cash > 0 || knet > 0) {
                    var userId = '@Model.Id';
                    const apiBaseUrl = '@ViewData["ApiBaseUrl"]'; // Fetch the base URL once
                    var url = `${apiBaseUrl}/api/v1/Payment/ManualPayment`;

                    var selectedItems = getSelectedItems();
                    var bank = $('#pay-bank').val() || 0;

                    var itemDetails = {
                        items: selectedItems,
                        fees: {
                            cash: cash,
                            knet: knet,
                            knetReference: knetReference,
                            online: bank
                        }
                    };
                    var itemName = JSON.stringify(itemDetails);

                    var paymentMethod = cash > 0 ? "Cash" : "Knet";

                    var data = {
                        UserId: userId,
                        ItemName: itemName,
                        TotalCost: cash > 0 ? cash : knet,
                        CreatedBy: '@ViewData["CreatedBy"]',
                        PaymentMethod: paymentMethod // Set the payment method
                    };

                    function onBegin() {
                        var submitButton = $('#main-form .action-button');
                        submitButton.hide();
                        if ($('#eform-loader').length === 0) {
                            submitButton.after('<button id="eform-loader" class="btn icon-btn m-3 btn-secondary" type="button" disabled><span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span> Processing...</button>');
                        }
                    }

                    $.ajax({
                        type: 'POST',
                        url: url,
                        contentType: 'application/json',
                        data: JSON.stringify(data),
                        beforeSend: onBegin,
                        success: function (response) {
                            $('#eform-loader').remove();
                            isSubmitting = false;
                            if (response.success) {
                                toastr.success('Manual payment processed successfully.');
                                setTimeout(function () {
                                    location.reload();
                                }, 1500);
                            } else {
                                toastr.error('Failed to process manual payment: ' + response.message);
                                $('.action-button').show();
                            }
                        },
                        error: function () {
                            $('#eform-loader').remove();
                            toastr.error('An error occurred while processing the manual payment.');
                            $('.action-button').show();
                            isSubmitting = false;
                        }
                    });
                } else {
                    $('#main-form').off('submit').submit();
                }
            }

            $('#main-form').submit(manualPayment);
        });
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const invoiceFilePathInput = document.getElementById('invoiceFilePath');
            const invoiceLink = document.getElementById('invoiceLink');
            const civilId = '@Model.CivilId'; // Assuming CivilId is available in the model
            const customerMobile = '@Model.MobileNumber'; // Assuming CustomerMobile is available in the model

            if (!invoiceFilePathInput.value) {
                getInvoicePath(civilId, customerMobile);
            } else {
                checkAndSetInvoiceLink(invoiceFilePathInput.value, "Bank");
            }

            async function getInvoicePath(civilId, customerMobile) {
                try {
                    const response = await fetch(`/Application/GetInvoicePath?civilId=${civilId}&customerMobile=${customerMobile}`);
                    const data = await response.json();

                    if (data.success && data.invoiceFilePath) {
                        // Update the hidden input field
                        invoiceFilePathInput.value = data.invoiceFilePath;

                        // Check and set the link
                        checkAndSetInvoiceLink(data.invoiceFilePath, data.paymentMethod);
                    } else {
                        hideInvoiceLink();
                    }
                } catch (error) {
                    console.error('Error fetching invoice path:', error);
                    hideInvoiceLink();
                }
            }

            async function checkAndSetInvoiceLink(path, paymentMethod) {
                let fullPath;
                if (paymentMethod === "Cash" || paymentMethod === "Knet") {
                    fullPath = `/api/v1/files/${path}`;
                } else {
                    fullPath = `/Files/${path}`;
                }

                try {
                    // Check if the file exists
                    const response = await fetch(fullPath, { method: 'HEAD' });
                    if (response.ok) {
                        updateInvoiceLink(fullPath);
                    } else {
                        hideInvoiceLink();
                    }
                } catch (error) {
                    console.error('Error checking invoice link:', error);
                    hideInvoiceLink();
                }
            }

            function updateInvoiceLink(path) {
                invoiceLink.href = path;
                invoiceLink.style.display = 'block'; // Show the link
            }

            function hideInvoiceLink() {
                invoiceLink.style.display = 'none'; // Hide the link
            }
        });
    </script>

    <script>
        $(document).ready(function () {
            // Fetch the current user's roles
            $.ajax({
                url: '/api/v1/UserRole/roles',
                method: 'GET',
                success: function (roles) {
                    applyRoles(roles);
                    console.log(roles);
                },
                error: function () {
                    console.error('Failed to fetch user roles');
                }
            });

            function applyRoles(roles) {
                const teamToFormMap = {
                    'Reception': ['#BasicInfo', '#ReceptionInfo'],
                    'Advising': ['#AdmissionInfo'],
                    'PlacementTest': ['#PlacementTestInfo'],
                    'ApplicationSubmission': ['#ApplicationInfo'],
                    'Payment': ['#PaymentsInfo', '#AdmissionInfo', '#FilesInfo', '#PlacementTestInfo', '#BasicInfo', '#ReceptionInfo', '#ApplicationInfo'],
                    'Documents': ['#FilesInfo', '#PlacementTestInfo']
                };

                const adminRole = 'Admin';
                const salesAgentRole = 'SalesAgent';
                const topManagementRole = 'TopManagement';

                // If the user has the admin or sales agent role, enable all forms and exit early
                if (roles.includes(adminRole) || roles.includes(salesAgentRole) || roles.includes(topManagementRole)) {
                    // Enable all forms for admin or sales agent
                    $('form input, form textarea, form button, form select').prop('disabled', false);
                    return;
                }

                // Disable all forms
                $('form input, form textarea, form button, form select').prop('disabled', true);

                // Enable CommentBox-form for all
                $('#CommentBox-form input, #CommentBox-form textarea, #CommentBox-form button').prop('disabled', false);

                // Enable forms based on the user's roles
                roles.forEach(role => {
                    if (teamToFormMap[role]) {
                        teamToFormMap[role].forEach(formId => {
                            $(formId + ' input, ' + formId + ' textarea, ' + formId + ' button, ' + formId + ' select').prop('disabled', false);
                        });
                    }
                });

                $('#Reception-Docs-form input, #Reception-Docs-form textarea, #Reception-Docs-form button, #Reception-Docs-form select').prop('disabled', true);
            }
        });
    </script>

    <script>
        $(document).ready(function () {
            function updateLink() {
                var email = $('#PUCEmail').val();
                var password = $('#PUCPassword').val();
                var CivilId = $('#civilId').val();

                var link = 'https://registration.puc.edu.kw/Account/Login?Email=' + email + '&CivilID=' + CivilId + '&Password=' + password;
                $('#PUCLoginLink').attr('href', link);

                var link2 = 'https://registration.puc.edu.kw/Account/Register?Email=' + email + '&CivilID=' + CivilId + '&Password=' + password;
                $('#PUCRegisterLink').attr('href', link2);
            }

            // Call updateLink on page load
            updateLink();

            // Call updateLink whenever the input fields change
            $('#PUCEmail, #PUCPassword, #CivilId').on('input', updateLink);
        });
    </script>

    <script>
        document.querySelectorAll('.action-button').forEach(button => {
            button.addEventListener('click', function (event) {
                var applicationId = '@Model.Id';
                var actionUrl = '';
                var statusValue = '';

                if (this.id === 'mark-complete-btn') {
                    event.preventDefault();
                    actionUrl = '/Application/MarkAsComplete/' + applicationId;
                    statusValue = 'Complete';
                } else if (this.id === 'mark-incomplete-btn') {
                    event.preventDefault();
                    actionUrl = '/Application/MarkAsIncomplete/' + applicationId;
                    statusValue = 'Incomplete';
                }

                if (actionUrl !== '') {
                    $.ajax({
                        url: actionUrl,
                        type: 'POST',
                        success: function (response) {
                            if (response.success) {
                                document.getElementById('ApplicationStatus').value = statusValue;
                                // toastr.success('Application status updated successfully.');
                                setTimeout(function () {
                                    location.reload();
                                }, 1500);
                            } else {
                                toastr.error('Failed to update application status. Please try again.');
                            }
                        },
                        error: function () {
                            toastr.error('An error occurred while updating the application status.');
                        }
                    });
                }
            });
        });
    </script>
}