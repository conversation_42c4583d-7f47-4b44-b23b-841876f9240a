﻿@using Newtonsoft.Json
@using SRJ.CommonCore.ViewModels
@model PaymentStatusTableVM

@{
    ViewData["Title"] = "Edit Payment Status";
    ViewData["Back"] = true;
}

<div class="row">
    <div class="col">
        <nav aria-label="breadcrumb" class="bg-body-tertiary rounded-3 p-3 mb-4">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item"><a href="/PaymentStatus">Payment Status</a></li>
                <li class="breadcrumb-item active" aria-current="page">Edit Payment Status</li>
            </ol>
        </nav>
    </div>
</div>

<form enctype="multipart/form-data" asp-action="Edit">
    <div class="row">
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-body text-center">
                    @if (Model.InvoiceFilePath != null)
                    {
                        <div class="row mt-3">
                            <div class="col-12">
                                <iframe src="@(Model.PaymentMethod == "Cash" || Model.PaymentMethod == "Knet" ? $"{ViewBag.FileApiBaseUrl}/Files/{Model.InvoiceFilePath}": $"/Files/{Model.InvoiceFilePath}")"
                                        style="width: 100%; height: 400px;" frameborder="0"></iframe>
                            </div>
                            <div class="col-12 my-3">
                                <a href="@(Model.PaymentMethod == "Cash" || Model.PaymentMethod == "Knet" ? $"{ViewBag.FileApiBaseUrl}/Files/{Model.InvoiceFilePath}": $"/Files/{Model.InvoiceFilePath}")"
                                   target="_blank" class="btn btn-outline-info" style="display: block;">
                                    <i class="bi bi-box-arrow-up-right"></i>
                                    Open Invoice in New Tab
                                </a>
                            </div>
                        </div>
                    }

                    <div class="form-group mt-3">
                        <button type="button" id="check-payment-status" class="btn btn-secondary ms-2" onclick="checkPaymentStatus()">
                            <i class="fa fa-refresh fa-fw me-1"></i> Check Payment Status
                        </button>
                    </div>

                    <h5 class="my-3">@Model.CustomerName</h5>
                    <p class="text-muted mb-1">Invoice Id: @Model.InvoiceId</p>
                    <p class="text-muted mb-4">Created on @Model.CreatedDate.ToString("dd/MM/yyyy hh:mm tt")</p>
                </div>
            </div>
        </div>
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-body">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                    <!-- Bootstrap 5 Tabs -->
                    <ul class="nav nav-tabs" id="paymentStatusTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="invoice-details-tab" data-bs-toggle="tab" data-bs-target="#invoice-details" type="button" role="tab" aria-controls="invoice-details" aria-selected="true">Invoice Details</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="customer-details-tab" data-bs-toggle="tab" data-bs-target="#customer-details" type="button" role="tab" aria-controls="customer-details" aria-selected="false">Customer Details</button>
                        </li>
                    </ul>

                    <div class="tab-content" id="paymentStatusTabContent">
                        <!-- Invoice Details Tab -->
                        <div class="tab-pane fade show active" id="invoice-details" role="tabpanel" aria-labelledby="invoice-details-tab">
                            <div class="row mt-3">
                                <div class="col-sm-3">
                                    <p class="mb-0">Invoice ID</p>
                                </div>
                                <div class="col-sm-9">
                                    <input asp-for="InvoiceId" class="form-control text-muted mb-0" />
                                    <span asp-validation-for="InvoiceId" class="text-danger"></span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-3">
                                    <p class="mb-0">Invoice Status</p>
                                </div>
                                <div class="col-sm-9">
                                    <select asp-for="InvoiceStatus" class="form-control text-muted mb-0 form-select">
                                        <option value="Pending">Pending</option>
                                        <option value="Paid">Paid</option>
                                    </select>
                                    <span asp-validation-for="InvoiceStatus" class="text-danger"></span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-3">
                                    <p class="mb-0">Cost</p>
                                </div>
                                <div class="col-sm-9">
                                    <input asp-for="Cost" class="form-control text-muted mb-0" />
                                    <span asp-validation-for="Cost" class="text-danger"></span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-3">
                                    <p class="mb-0">Payment Method</p>
                                </div>
                                <div class="col-sm-9">
                                    <select asp-for="PaymentMethod" class="form-control text-muted mb-0 form-select" id="PaymentMethod">
                                        <option value="Cash">Cash</option>
                                        <option value="Knet">Knet</option>
                                        <option value="Bank">Bank</option>
                                    </select>
                                    <span asp-validation-for="PaymentMethod" class="text-danger"></span>
                                </div>
                            </div>
                            <input type="hidden" id="UserDefinedField" name="UserDefinedField" />
                            <!-- KNET Reference Input -->
                            <hr id="knetReferenceWrapperHR" style="display: none;">
                            <div class="row" id="knetReferenceWrapper" style="display: none;">
                                <div class="col-sm-3">
                                    <p class="mb-0">KNET Reference</p>
                                </div>
                                <div class="col-sm-9">
                                    <input type="text" id="knetReference" class="form-control text-muted mb-0" />
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-3">
                                    <p class="mb-0">Transaction Date</p>
                                </div>
                                <div class="col-sm-9">
                                    <input asp-for="TransactionDate" type="date" class="form-control text-muted mb-0" />
                                    <span asp-validation-for="TransactionDate" class="text-danger"></span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-3">
                                    <p class="mb-0">Collector Name</p>
                                </div>
                                <div class="col-sm-9">
                                    <input asp-for="Collector" class="form-control text-muted mb-0" />
                                    <span asp-validation-for="Collector" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Details Tab -->
                        <div class="tab-pane fade" id="customer-details" role="tabpanel" aria-labelledby="customer-details-tab">
                            <div class="row mt-3">
                                <div class="col-sm-3">
                                    <p class="mb-0">Customer Name</p>
                                </div>
                                <div class="col-sm-9">
                                    <input asp-for="CustomerName" class="form-control text-muted mb-0" />
                                    <span asp-validation-for="CustomerName" class="text-danger"></span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-3">
                                    <p class="mb-0">Civil ID</p>
                                </div>
                                <div class="col-sm-9">
                                    <input asp-for="CivilId" class="form-control text-muted mb-0" />
                                    <span asp-validation-for="CivilId" class="text-danger"></span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-3">
                                    <p class="mb-0">Mobile Number</p>
                                </div>
                                <div class="col-sm-9">
                                    <input asp-for="MobileNumber" class="form-control text-muted mb-0" />
                                    <span asp-validation-for="MobileNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Save Changes Button -->
                    <div class="form-group mt-3">
                        <button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw me-1"></i> Save changes</button>
                    </div>

                    <!-- Generate Invoice Button -->
                    <div class="form-group mt-3">
                        <button type="button" id="generate-invoice-button" class="btn btn-secondary" onclick="generateInvoice()">
                            <i class="fa fa-file-invoice fa-fw me-1"></i> Generate Invoice
                        </button>
                    </div>
                </div>
            </div>

            <div>
                <div id="payment-status-result" class="mt-4"></div>
            </div>
        </div>
    </div>

    <!-- Hidden Fields for all other properties -->
    <input type="hidden" asp-for="Id" />
    <input type="hidden" asp-for="Name" />
    <input type="hidden" asp-for="Email" />
    <input type="hidden" asp-for="ItemName" />
    <input type="hidden" asp-for="ktechId" />
    <input type="hidden" asp-for="IsSuccess" />
    <input type="hidden" asp-for="Message" />
    <input type="hidden" asp-for="ValidationErrors" />
    <input type="hidden" asp-for="PaymentId" />
    <input type="hidden" asp-for="InvoiceReference" />
    <input type="hidden" asp-for="CustomerReference" />
    <input type="hidden" asp-for="ExpiryDate" />
    <input type="hidden" asp-for="InvoiceValue" />
    <input type="hidden" asp-for="CustomerMobile" />
    <input type="hidden" asp-for="CustomerEmail" />
    <input type="hidden" asp-for="InvoiceItems" />
    <input type="hidden" asp-for="InvoiceTransactions" />
    <input type="hidden" asp-for="InvoiceFilePath" />
    <input type="hidden" asp-for="Semester" />
    <input type="hidden" asp-for="TransactionDate" />
    <input type="hidden" asp-for="CreatedDate" />
</form>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }

    <script>
        $(document).ready(function () {
            // Handle payment method change
            $('#PaymentMethod').on('change', function () {
                var selectedMethod = $(this).val();
                var cost = '@Model.Cost'; // assuming the cost is bound to the model

                var userDefinedField = {
                    items: 'PUC Fees',
                    fees: {
                        cash: selectedMethod === 'Cash' ? cost : 0,
                        knet: selectedMethod === 'Knet' ? cost : 0,
                        knetReference: '',
                        online: selectedMethod === 'Bank' ? cost : 0
                    }
                };

                if (selectedMethod === 'Knet') {
                    $('#knetReferenceWrapper').show();
                    $('#knetReferenceWrapperHR').show();
                } else {
                    $('#knetReferenceWrapper').hide();
                    $('#knetReferenceWrapperHR').hide();
                }

                $('#UserDefinedField').val(JSON.stringify(userDefinedField));
            });

            // Initial trigger to handle the case if form is already populated
            $('#PaymentMethod').trigger('change');
        });

        function updateUserDefinedField() {
            var knetReference = $('#knetReference').val();
            var userDefinedField = JSON.parse($('#UserDefinedField').val());

            userDefinedField.fees.knetReference = knetReference;

            $('#UserDefinedField').val(JSON.stringify(userDefinedField));
        }

        $('#knetReference').on('input', updateUserDefinedField);


        function generateInvoice() {
            // Collect the form data
            var formData = new FormData($('form')[0]);

            // Show loading screen or spinner
            $('#payment-status-result').html(`
                                        <div class="text-center my-3">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Generating Invoice...</span>
                                            </div>
                                            <p>Generating invoice...</p>
                                        </div>
                                    `);

            $.ajax({
                url: `/PaymentStatus/CreateInvoice`,
                type: 'POST',
                processData: false,
                contentType: false,
                data: formData,
                success: function (response) {
                    console.log("Response received:", response); // Log the response for debugging

                    // Ensure the loading message is cleared
                    $('#payment-status-result').empty();

                    if (response.success) {
                        // Determine the full invoice URL based on the payment method
                        var invoiceUrl = response.paymentMethod === "Cash" || response.paymentMethod === "Knet"
                            ? `${ViewBag.FileApiBaseUrl}/Files/${response.invoiceUrl}`
                            : `/Files/${response.invoiceUrl}`;

                        // Update iframe and link
                        $('iframe').attr('src', invoiceUrl);
                        $('a[target="_blank"]').attr('href', invoiceUrl);

                        // Show success message and the link to the generated invoice
                        $('#payment-status-result').html(`
                                                    <div class="alert alert-success">
                                                        Invoice generated successfully. <a href="${invoiceUrl}" target="_blank">View Invoice</a>
                                                    </div>
                                                `);
                    } else {
                        $('#payment-status-result').html('<div class="alert alert-warning">Failed to generate invoice. Please try again.</div>');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.error("AJAX error:", textStatus, errorThrown); // Log errors for debugging

                    // Ensure the loading message is cleared
                    $('#payment-status-result').empty();

                    $('#payment-status-result').html('<div class="alert alert-danger">An error occurred while generating the invoice.</div>');
                }
            });
        }
    </script>

    <script>
        function checkPaymentStatus() {
            var paymentMethod = '@Model.PaymentMethod';
            var invoiceId = '@Model.InvoiceId';
            const apiBaseUrl = '@ViewData["ApiBaseUrl"]'; // Fetch the base URL once
            var url = `${apiBaseUrl}/api/v1/Payment/GetPaymentStatusByInvoiceId/${invoiceId}`;

            if (paymentMethod === 'Bank') {
                // Show loading screen
                $('#payment-status-result').html(`
                                    <div class="text-center my-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p>Loading payment status...</p>
                                    </div>
                                `);

                $.ajax({
                    url: url,
                    type: 'GET',
                    success: function (response) {
                        if (response.success) {
                            // Handle the successful response here
                            var data = response.data.data;
                            var resultHtml = `
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5>Payment Status Details</h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <p><strong>Invoice ID:</strong> ${data.invoiceId}</p>
                                                        <p><strong>Invoice Status:</strong> ${data.invoiceStatus}</p>
                                                        <p><strong>Customer Name:</strong> ${data.customerName}</p>
                                                        <p><strong>Customer Mobile:</strong> ${data.customerMobile}</p>
                                                        <p><strong>Invoice Value:</strong> ${data.invoiceDisplayValue}</p>
                                                        <p><strong>Transaction Date:</strong> ${data.createdDate}</p>
                                                    </div>
                                                </div>
                                            `;
                            $('#payment-status-result').html(resultHtml);
                        } else {
                            $('#payment-status-result').html('<div class="alert alert-warning">Payment status not found or error occurred.</div>');
                        }
                    },
                    error: function () {
                        $('#payment-status-result').html('<div class="alert alert-danger">An error occurred while retrieving the payment status.</div>');
                    }
                });
            } else {
                $('#payment-status-result').html('<div class="alert alert-info">Payment status check is only available for Bank payment method.</div>');
            }
        }
    </script>
}