using AspNetCore.Unobtrusive.Ajax;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.UI;
using NToastNotify;
using SRJ.WebCore.ACL;
using SRJ.WebCore.Extensions;
using SRJ.WebCore.Middlewares;
using Microsoft.AspNetCore.Http.Features;

var builder = WebApplication.CreateBuilder(args);

builder.Services.Configure<FormOptions>(options =>
{
    options.ValueCountLimit = int.MaxValue;
});

builder.Services.ConfigureAzureAdAuthentication(builder.Configuration);
builder.Services.ConfigureLoggerService();
builder.Services.ConfigureMSSqlServer(builder.Configuration);
//builder.Services.ConfigureSQLite(builder.Configuration);
builder.Services.ConfigureIdentity();
builder.Services.ConfigureAutoMapper();
builder.Services.ConfigureCookies();
builder.Services.ConfigureCaching(builder.Configuration);
builder.Services.AddControllersWithCustomJsonOptions();

builder.Services.ConfigureRepository();
builder.Services.ConfigureSettings(builder.Configuration);

builder.Services.AddScoped<DynamicAuthorizationFilter>();

builder.Services.AddControllersWithViews(options =>
{
    var policy = new AuthorizationPolicyBuilder()
        .RequireAuthenticatedUser()
        .Build();
    options.Filters.Add(new AuthorizeFilter(policy));
    options.Filters.Add(new ServiceFilterAttribute(typeof(DynamicAuthorizationFilter)));

    // Add CSRF protection
    options.Filters.Add(new AutoValidateAntiforgeryTokenAttribute());
}).AddRazorRuntimeCompilation().AddNToastNotifyToastr(new ToastrOptions()
{
    ProgressBar = false,
    CloseButton = true,
    TapToDismiss = true,
    PositionClass = ToastPositions.BottomRight
});

builder.Services.AddRazorPages()
    .AddMicrosoftIdentityUI();

builder.Services.AddUnobtrusiveAjax();

builder.Services.AddHttpContextAccessor();

// Configure antiforgery
builder.Services.AddAntiforgery(options =>
{
    options.HeaderName = "X-CSRF-TOKEN";
    options.Cookie.Name = "__RequestVerificationToken";
    options.Cookie.HttpOnly = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
    options.Cookie.SameSite = SameSiteMode.Strict;
});

// Sidebar dynamic menu items json
builder.Configuration.AddJsonFile("sidebar.json",
     optional: true,
     reloadOnChange: true);

builder.Services.AddCors(options =>
{
    var allowedOrigins = builder.Configuration.GetSection("CorsSettings:AllowedOrigins").Get<string[]>();

    options.AddPolicy("CorsPolicy",
        builder => builder.WithOrigins(allowedOrigins)
                          .AllowAnyHeader()
                          .AllowAnyMethod()
                          .AllowCredentials());
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}
else
{
    app.UseStatusCodePagesWithReExecute("/Error/{0}");
}

app.UseHttpsRedirection();
app.UseStaticFiles();

// Add security headers
app.UseSecurityHeaders();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.UseNToastNotify();

// It is required for serving 'jquery-unobtrusive-ajax.min.js' embedded script file.
app.UseUnobtrusiveAjax();

app.UseCors("CorsPolicy");

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Application}/{action=Index}/{id?}");

app.MapRazorPages();
app.MapControllers(); // Ensure API controllers are mapped

app.Run();