﻿@using Newtonsoft.Json
@using SRJ.CommonCore.ViewModels
@model PaymentStatusTableVM

@{
    ViewData["Title"] = "Payment Status Details";
    ViewData["Back"] = true;
}

<div class="row">
    <div class="col">
        <nav aria-label="breadcrumb" class="bg-body-tertiary rounded-3 p-3 mb-4">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item"><a href="/PaymentStatus">Payment Status</a></li>
                <li class="breadcrumb-item active" aria-current="page">Details</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-body text-center">
                @if (Model.InvoiceFilePath != null)
                {
                    <div class="row mt-3">
                        <div class="col-12">
                            <iframe src="@(Model.PaymentMethod == "Cash" || Model.PaymentMethod == "Knet" ? $"{ViewBag.FileApiBaseUrl}/Files/{Model.InvoiceFilePath}": $"/Files/{Model.InvoiceFilePath}")"
                                    style="width: 100%; height: 400px;" frameborder="0"></iframe>
                        </div>
                        <div class="col-12 my-3">
                            <a href="@(Model.PaymentMethod == "Cash" || Model.PaymentMethod == "Knet" ? $"{ViewBag.FileApiBaseUrl}/Files/{Model.InvoiceFilePath}": $"/Files/{Model.InvoiceFilePath}")"
                               target="_blank" class="btn btn-outline-info" style="display: block;">
                                <i class="bi bi-box-arrow-up-right"></i>
                                Open Invoice in New Tab
                            </a>
                        </div>
                    </div>
                }

                <div class="form-group mt-3">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary ms-2">
                        Edit
                        <i class="bi bi-pencil-square"></i>
                    </a>
                </div>

                <div class="form-group mt-3">
                    <button type="button" id="check-payment-status" class="btn btn-secondary ms-2" onclick="checkPaymentStatus()">
                        <i class="fa fa-refresh fa-fw me-1"></i> Check Payment Status
                    </button>
                </div>

                <h5 class="my-3">@Model.CustomerName</h5>
                <p class="text-muted mb-1">Invoice Id: @Model.InvoiceId</p>
                <p class="text-muted mb-4">Created on @Model.CreatedDate.ToString("dd/MM/yyyy hh:mm tt")</p>
            </div>
        </div>
    </div>
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-3">
                        <p class="mb-0">Invoice ID</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0">@Model.InvoiceId</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-3">
                        <p class="mb-0">Invoice Status</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0">@Model.InvoiceStatus</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-3">
                        <p class="mb-0">Cost</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0">@Model.Cost</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-3">
                        <p class="mb-0">Payment Method</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0">@Model.PaymentMethod</p>
                    </div>
                </div>
                <hr>
                @if (Model.PaymentMethod == "Knet")
                {
                    <div class="row">
                        <div class="col-sm-3">
                            <p class="mb-0">KNET Reference</p>
                        </div>
                        <div class="col-sm-9">
                            <p class="text-muted mb-0">Model.UserDefinedField.fees.knetReference</p>
                        </div>
                    </div>
                    <hr>
                }
                <div class="row">
                    <div class="col-sm-3">
                        <p class="mb-0">Collector</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0">@Model.Collector</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-3">
                        <p class="mb-0">Customer Name</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0">@Model.CustomerName</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-3">
                        <p class="mb-0">Civil ID</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0">@Model.CivilId</p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-3">
                        <p class="mb-0">Mobile Number</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0">@Model.MobileNumber</p>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <div id="payment-status-result" class="mt-4"></div>
        </div>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
    <script>
        function checkPaymentStatus() {
            var paymentMethod = '@Model.PaymentMethod';
            var invoiceId = '@Model.InvoiceId';
            const apiBaseUrl = '@ViewData["ApiBaseUrl"]'; // Fetch the base URL once
            var url = `${apiBaseUrl}/api/v1/Payment/GetPaymentStatusByInvoiceId/${invoiceId}`;

            // Show loading screen
            $('#payment-status-result').html(`
                        <div class="text-center my-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading payment status...</p>
                        </div>
                    `);

            $.ajax({
                url: url,
                type: 'GET',
                success: function (response) {
                    if (response.success) {
                        var data = response.data.data;

                        var resultHtml = `
                                    <div class="card">
                                        <div class="card-header">
                                            <h5>Payment Status Details</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-sm-4">
                                                    <p class="mb-0"><strong>Invoice ID:</strong></p>
                                                </div>
                                                <div class="col-sm-8">
                                                    <p class="text-muted mb-0">${data.invoiceId}</p>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-4">
                                                    <p class="mb-0"><strong>Invoice Status:</strong></p>
                                                </div>
                                                <div class="col-sm-8">
                                                    <p class="text-muted mb-0">${data.invoiceStatus}</p>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-4">
                                                    <p class="mb-0"><strong>Customer Name:</strong></p>
                                                </div>
                                                <div class="col-sm-8">
                                                    <p class="text-muted mb-0">${data.customerName}</p>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-4">
                                                    <p class="mb-0"><strong>Customer Mobile:</strong></p>
                                                </div>
                                                <div class="col-sm-8">
                                                    <p class="text-muted mb-0">${data.customerMobile}</p>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-4">
                                                    <p class="mb-0"><strong>Invoice Value:</strong></p>
                                                </div>
                                                <div class="col-sm-8">
                                                    <p class="text-muted mb-0">${data.invoiceDisplayValue}</p>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-sm-4">
                                                    <p class="mb-0"><strong>Transaction Date:</strong></p>
                                                </div>
                                                <div class="col-sm-8">
                                                    <p class="text-muted mb-0">${data.createdDate}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;
                        $('#payment-status-result').html(resultHtml);
                    } else {
                        $('#payment-status-result').html('<div class="alert alert-warning">Payment status not found or error occurred.</div>');
                    }
                },
                error: function () {
                    $('#payment-status-result').html('<div class="alert alert-danger">An error occurred while retrieving the payment status.</div>');
                }
            });
        }
    </script>
}